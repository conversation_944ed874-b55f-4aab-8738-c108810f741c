{"name": "startwrite-monorepo", "private": true, "version": "0.0.0", "packageManager": "pnpm@10.12.4", "scripts": {"build": "turbo build", "dev": "turbo dev --parallel", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "prisma:generate": "cd packages/api && pnpm prisma:generate", "predev": "npx kill-port 4000 && npx kill-port 5173"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tanstack/router-devtools": "^1.131.26", "@tanstack/router-vite-plugin": "^1.131.26", "@types/node": "^20.19.11", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "dotenv": "^17.2.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "pino-pretty": "^13.1.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "turbo": "^2.2.14", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.1.3", "wait-on": "^8.0.4"}, "dependencies": {"@ai-sdk/google": "^2.0.7", "@ai-sdk/react": "^2.0.15", "@hono/node-server": "^1.19.0", "@tanstack/react-router": "^1.131.26", "@types/bcrypt": "^6.0.0", "@types/pg": "^8.15.5", "ai": "^5.0.15", "bcrypt": "^6.0.0", "better-auth": "^1.3.7", "hono": "^4.9.2", "pg": "^8.16.3", "pino": "^9.9.0", "tsx": "^4.20.4"}}
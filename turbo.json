{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "env": ["VERCEL_URL", "VITE_VERCEL_URL", "GOOGLE_GENERATIVE_AI_API_KEY", "BETTER_AUTH_URL", "BETTER_AUTH_SECRET"]}, "dev": {"cache": false, "persistent": true, "env": ["VERCEL_URL", "VITE_VERCEL_URL", "GOOGLE_GENERATIVE_AI_API_KEY", "BETTER_AUTH_URL", "BETTER_AUTH_SECRET"]}, "lint": {"dependsOn": ["^lint"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}, "prisma:generate": {"cache": false}, "auth:generate": {"cache": false}, "type-check": {"dependsOn": ["^type-check"]}}}
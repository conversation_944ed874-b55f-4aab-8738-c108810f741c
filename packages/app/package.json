{"name": "@startwrite/app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "vite build", "build:prod": "VERCEL_URL=http://startwrite.vercel.app VITE_VERCEL_URL=https://startwrite.vercel.app vite build", "dev": "vite", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest", "clean": "rm -rf dist", "auth:generate": "npx @better-auth/cli generate"}, "dependencies": {"@ai-sdk/google": "^2.0.6", "@ai-sdk/react": "^2.0.15", "@tanstack/react-router": "^1.131.26", "@tiptap/extension-character-count": "^3.2.0", "@tiptap/extension-focus": "^3.2.0", "@tiptap/extension-placeholder": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/react": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "ai": "^5.0.15", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@tanstack/router-devtools": "^1.131.26", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^2.0.5", "autoprefixer": "^10.4.18", "better-auth": "^1.3.7", "jsdom": "^25.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.20.4", "typescript": "^5.5.3", "vite": "^7.1.3", "vitest": "^2.0.5"}}
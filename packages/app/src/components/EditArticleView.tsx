import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from '@tanstack/react-router';

type Article = {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
};

const EditArticleView: React.FC = () => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const { articleId } = useParams({ from: '/articles/$articleId' });
  const navigate = useNavigate();

  useEffect(() => {
    const fetchArticle = async () => {
      const response = await fetch(`/api/articles/${articleId}`);
      const data: Article = await response.json();
      setTitle(data.title);
      setContent(data.content);
    };
    fetchArticle();
  }, [articleId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await fetch(`/api/articles/${articleId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ title, content }),
    });
    navigate({ to: '/articles' });
  };

  return (
    <div>
      <h1>Edit Article</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label>Title</label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>
        <div>
          <label>Content</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
          />
        </div>
        <button type="submit">Update</button>
      </form>
    </div>
  );
};

export default EditArticleView;

import React from 'react';
import { PenTool, BookOpen, Sparkles, Settings, Menu, LogIn } from 'lucide-react';
import { Link } from '@tanstack/react-router';

interface NavigationProps {
  onToggleAI: () => void;
  onToggleWritingTools: () => void;
}

export function Navigation({ onToggleAI, onToggleWritingTools }: NavigationProps) {
  const navItems = [
    { to: '/notes', label: 'Notes', icon: PenTool },
    { to: '/articles', label: 'Articles', icon: BookOpen },
    { to: '/login', label: 'Articles', icon: BookOpen },
    { to: '/', label: 'Tools', icon: Sparkles },
    { to: '/login', label: 'Login', icon: Sparkles },
  ];

  return (
    <nav className="bg-white border-b border-slate-200 px-6 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-8">
          <div className="text-xl font-semibold text-slate-900">Mindful</div>
          
          <div className="hidden md:flex space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.to}
                  to={item.to}
                  className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                  activeProps={{
                    className: 'bg-indigo-50 text-indigo-700 font-medium',
                  }}
                >
                  <Icon size={18} />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Link
            to="/login"
            className="flex items-center space-x-2 px-4 py-2 rounded-lg text-slate-600 hover:text-indigo-700 hover:bg-indigo-50 transition-all duration-200"
            activeProps={{
              className: 'bg-indigo-50 text-indigo-700 font-medium',
            }}
          >
            <LogIn size={18} />
            <span className="hidden sm:inline">Login</span>
          </Link>
          <button
            onClick={onToggleAI}
            className="flex items-center space-x-2 px-4 py-2 rounded-lg text-slate-600 hover:text-indigo-700 hover:bg-indigo-50 transition-all duration-200"
          >
            <Sparkles size={18} />
            <span className="hidden sm:inline">AI Assist</span>
          </button>
          
          <button
            onClick={onToggleWritingTools}
            className="flex items-center space-x-2 px-4 py-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-50 transition-all duration-200"
          >
            <PenTool size={18} />
            <span className="hidden sm:inline">Tools</span>
          </button>
          
          <button className="p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-50 transition-all duration-200">
            <Settings size={18} />
          </button>
        </div>
      </div>
    </nav>
  );
}
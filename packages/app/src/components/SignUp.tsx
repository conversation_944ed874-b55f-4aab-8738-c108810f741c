import { useState } from 'react';
import { useAuth } from '../hooks/useAuth';

export function SignUp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  
  const { 
    isAuthenticated, 
    isPending, 
    signUp, 
    isLoading, 
    error, 
    clearError,
    session 
  } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    await signUp(email, password, name);
  };

  // If user is already authenticated, show success message
  if (isAuthenticated) {
    return (
      <div>
        <h2>Welcome!</h2>
        <p>You are successfully registered and logged in as {session?.user.email}</p>
      </div>
    );
  }

  return (
    <div>
      <h2>Sign Up</h2>
      {isPending && <p>Loading session...</p>}
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="name">Name</label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>
        <div>
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>
        <div>
          <label htmlFor="password">Password</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>
        <button type="submit" disabled={isLoading}>
          {isLoading ? 'Signing up...' : 'Sign Up'}
        </button>
        {error && <p style={{ color: 'red' }}>{error}</p>}
      </form>
    </div>
  );
}

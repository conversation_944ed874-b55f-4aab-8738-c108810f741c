import { useState } from 'react';
import { useAuth } from '../hooks/useAuth';

export function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  
  const { 
    session, 
    isAuthenticated, 
    isPending, 
    signIn, 
    signUp,
    signOut, 
    isLoading, 
    error, 
    clearError 
  } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    if (isSignUp) {
      await signUp(email, password, name);
    } else {
      await signIn(email, password);
    }
  };

  // If user is already authenticated, show success message
  if (isAuthenticated) {
    return (
      <div>
        <h2>Welcome back!</h2>
        <p>You are already logged in as {session?.user.email}</p>
        <button onClick={signOut} disabled={isLoading}>
          {isLoading ? 'Signing out...' : 'Sign Out'}
        </button>
      </div>
    );
  }

  return (
    <div>
      <h2>{isSignUp ? 'Sign Up' : 'Login'}</h2>
      {isPending && <p>Loading session...</p>}
      <form onSubmit={handleSubmit}>
        {isSignUp && (
          <div>
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
              placeholder="Your full name"
            />
          </div>
        )}
        <div>
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>
        <div>
          <label htmlFor="password">Password</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>
        <button type="submit" disabled={isLoading}>
          {isLoading 
            ? (isSignUp ? 'Creating account...' : 'Signing in...') 
            : (isSignUp ? 'Sign Up' : 'Login')
          }
        </button>
        {error && <p style={{ color: 'red' }}>{error}</p>}
      </form>
      
      <div style={{ marginTop: '1rem' }}>
        <button 
          type="button" 
          onClick={() => {
            setIsSignUp(!isSignUp);
            clearError();
          }}
          disabled={isLoading}
          style={{ 
            background: 'none', 
            border: 'none', 
            color: 'blue', 
            textDecoration: 'underline', 
            cursor: 'pointer' 
          }}
        >
          {isSignUp 
            ? 'Already have an account? Sign in' 
            : "Don't have an account? Sign up"
          }
        </button>
      </div>
    </div>
  );
}

import React, { useEffect, useState } from 'react';
import { Link } from '@tanstack/react-router';

type Article = {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
};

const ArticleListView: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);

  useEffect(() => {
    const fetchArticles = async () => {
      const response = await fetch('/api/articles');
      const data = await response.json();
      setArticles(data);
    };
    fetchArticles();
  }, []);

  const handleDelete = async (id: string) => {
    await fetch(`/api/articles/${id}`, {
      method: 'DELETE',
    });
    setArticles(articles.filter((a) => a.id !== id));
  };

  return (
    <div>
      <h1>Articles</h1>
      <Link to="/articles/new">New Article</Link>
      <ul>
        {articles.map((article) => (
          <li key={article.id}>
            <Link to={`/articles/${article.id}`}>{article.title}</Link>
            <button onClick={() => handleDelete(article.id)}>Delete</button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ArticleListView;

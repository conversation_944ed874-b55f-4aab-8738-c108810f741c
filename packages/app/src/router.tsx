import React from 'react';
import { RootRoute, Route, Router } from '@tanstack/react-router';
import App from './App';
import NotesView from './components/NotesView';
import { SignUp } from './components/SignUp';
import { Login } from './components/Login';
import ArticleListView from './components/ArticleListView';
import NewArticleView from './components/NewArticleView';
import EditArticleView from './components/EditArticleView';

const rootRoute = new RootRoute({
  component: App,
});


const notesRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/notes',
  component: NotesView,
});


const loginRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: Login,
});

const signUpRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/signup',
  component: SignUp,
});

const articlesRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/articles',
  component: ArticleListView,
});

const newArticleRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/articles/new',
  component: NewArticleView,
});

const editArticleRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/articles/$articleId',
  component: EditArticleView,
});


const routeTree = rootRoute.addChildren([
  notesRoute,
  loginRoute,
  signUpRoute,
  articlesRoute,
  newArticleRoute,
  editArticleRoute,
]);

export const router = new Router({ routeTree });

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

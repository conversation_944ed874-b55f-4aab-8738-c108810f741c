import { useState } from 'react';
import { authClient } from '../lib/auth-client';

export function useAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Use better-auth's built-in session hook
  const { data: session, isPending, error: sessionError, refetch } = authClient.useSession();

  const signIn = async (email: string, password: string) => {
    setError(null);
    setIsLoading(true);
    
    try {
      await authClient.signIn.email({ 
        email, 
        password 
      }, {
        onSuccess: () => {
          // Session will be automatically updated via useSession hook
          refetch();
        },
        onError: (ctx) => {
          setError(ctx.error.message || 'Sign in failed');
        }
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, name: string = '') => {
    setError(null);
    setIsLoading(true);
    
    try {
      await authClient.signUp.email({ 
        email, 
        password,
        name
      }, {
        onSuccess: () => {
          refetch();
        },
        onError: (ctx) => {
          setError(ctx.error.message || 'Sign up failed');
        }
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setError(null);
    setIsLoading(true);
    
    try {
      await authClient.signOut();
      refetch();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sign out failed');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    // Session data
    session,
    isAuthenticated: !!session && !isPending,
    isPending,
    
    // Actions
    signIn,
    signUp,
    signOut,
    refetch,
    
    // State
    isLoading,
    error: error || (sessionError?.message ?? null),
    clearError: () => setError(null),
  };
}

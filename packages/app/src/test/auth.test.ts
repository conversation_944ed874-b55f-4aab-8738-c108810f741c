import { describe, it, expect, vi, beforeEach } from 'vitest';
import { authClient } from '../lib/auth-client';

// Mock the auth client
vi.mock('../lib/auth-client', () => ({
  authClient: {
    signUp: {
      email: vi.fn(),
    },
  },
}));

describe('User Authentication', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('User Sign Up', () => {
    it('should successfully create a user with valid data', async () => {
      const mockUserData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const mockResponse = {
        data: { id: '1', email: '<EMAIL>', name: 'Test User' },
        error: null,
      };

      vi.mocked(authClient.signUp.email).mockResolvedValue(mockResponse);

      const result = await authClient.signUp.email(mockUserData);

      expect(authClient.signUp.email).toHaveBeenCalledWith(mockUserData);
      expect(result.data).toEqual(mockResponse.data);
      expect(result.error).toBeNull();
    });

    it('should handle validation errors for missing required fields', async () => {
      const invalidUserData = {
        email: '',
        password: 'password123',
        name: 'Test User',
      };

      const mockErrorResponse = {
        data: null,
        error: {
          code: 'FAILED_TO_CREATE_USER',
          message: 'Failed to create user',
          details: {
            name: 'PrismaClientValidationError',
            clientVersion: '6.14.0',
          },
        },
      };

      vi.mocked(authClient.signUp.email).mockResolvedValue(mockErrorResponse);

      const result = await authClient.signUp.email(invalidUserData);

      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('FAILED_TO_CREATE_USER');
      expect(result.error?.details?.name).toBe('PrismaClientValidationError');
    });

    it('should handle validation errors for invalid email format', async () => {
      const invalidUserData = {
        email: 'invalid-email',
        password: 'password123',
        name: 'Test User',
      };

      const mockErrorResponse = {
        data: null,
        error: {
          code: 'FAILED_TO_CREATE_USER',
          message: 'Failed to create user',
          details: {
            name: 'PrismaClientValidationError',
            clientVersion: '6.14.0',
          },
        },
      };

      vi.mocked(authClient.signUp.email).mockResolvedValue(mockErrorResponse);

      const result = await authClient.signUp.email(invalidUserData);

      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('FAILED_TO_CREATE_USER');
    });

    it('should handle duplicate email errors', async () => {
      const duplicateUserData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const mockErrorResponse = {
        data: null,
        error: {
          code: 'EMAIL_ALREADY_EXISTS',
          message: 'User with this email already exists',
        },
      };

      vi.mocked(authClient.signUp.email).mockResolvedValue(mockErrorResponse);

      const result = await authClient.signUp.email(duplicateUserData);

      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('EMAIL_ALREADY_EXISTS');
    });
  });
});

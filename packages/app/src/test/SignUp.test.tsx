import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SignUp } from '../components/SignUp';

// Mock the auth client
vi.mock('../lib/auth-client', () => ({
  authClient: {
    signUp: {
      email: vi.fn(),
    },
  },
}));

describe('SignUp Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders sign up form with all required fields', () => {
    render(<SignUp />);
    
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
  });

  it('updates form fields when user types', async () => {
    const user = userEvent.setup();
    render(<SignUp />);
    
    const nameInput = screen.getByLabelText(/name/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(nameInput, 'John Doe');
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    
    expect(nameInput).toHaveValue('John Doe');
    expect(emailInput).toHaveValue('<EMAIL>');
    expect(passwordInput).toHaveValue('password123');
  });

  it('calls authClient.signUp.email with correct data on form submission', async () => {
    const { authClient } = await import('../lib/auth-client');
    const mockSignUp = vi.mocked(authClient.signUp.email);
    mockSignUp.mockResolvedValue({ data: { id: '1' }, error: null });

    const user = userEvent.setup();
    render(<SignUp />);
    
    await user.type(screen.getByLabelText(/name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    
    await user.click(screen.getByRole('button', { name: /sign up/i }));
    
    await waitFor(() => {
      expect(mockSignUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        name: 'John Doe',
      });
    });
  });

  it('displays error message when sign up fails', async () => {
    const { authClient } = await import('../lib/auth-client');
    const mockSignUp = vi.mocked(authClient.signUp.email);
    mockSignUp.mockResolvedValue({
      data: null,
      error: {
        code: 'FAILED_TO_CREATE_USER',
        message: 'Failed to create user',
        details: {
          name: 'PrismaClientValidationError',
          clientVersion: '6.14.0',
        },
      },
    });

    const user = userEvent.setup();
    render(<SignUp />);
    
    await user.type(screen.getByLabelText(/name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    
    await user.click(screen.getByRole('button', { name: /sign up/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/failed to create user/i)).toBeInTheDocument();
    });
  });

  it('displays error message when exception is thrown', async () => {
    const { authClient } = await import('../lib/auth-client');
    const mockSignUp = vi.mocked(authClient.signUp.email);
    mockSignUp.mockRejectedValue(new Error('Network error'));

    const user = userEvent.setup();
    render(<SignUp />);
    
    await user.type(screen.getByLabelText(/name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    
    await user.click(screen.getByRole('button', { name: /sign up/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/network error/i)).toBeInTheDocument();
    });
  });

  it('clears error message when form is resubmitted', async () => {
    const { authClient } = await import('../lib/auth-client');
    const mockSignUp = vi.mocked(authClient.signUp.email);
    
    // First call fails
    mockSignUp.mockResolvedValueOnce({
      data: null,
      error: { message: 'Failed to create user' },
    });
    
    // Second call succeeds
    mockSignUp.mockResolvedValueOnce({
      data: { id: '1' },
      error: null,
    });

    const user = userEvent.setup();
    render(<SignUp />);
    
    // Fill form and submit (first attempt - fails)
    await user.type(screen.getByLabelText(/name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /sign up/i }));
    
    // Wait for error to appear
    await waitFor(() => {
      expect(screen.getByText(/failed to create user/i)).toBeInTheDocument();
    });
    
    // Submit again (second attempt - succeeds)
    await user.click(screen.getByRole('button', { name: /sign up/i }));
    
    // Error should be cleared
    await waitFor(() => {
      expect(screen.queryByText(/failed to create user/i)).not.toBeInTheDocument();
    });
  });
});

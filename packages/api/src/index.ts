import { Hono } from 'hono'
import { createMiddleware } from 'hono/factory'
import { logger } from 'hono/logger'
import { requestId } from 'hono/request-id'
import { cors } from 'hono/cors'
import { streamText, convertToModelMessages, type UIMessage } from 'ai'
import { google } from '@ai-sdk/google'
import { auth } from './lib/auth'
import path from 'path'
import fs from 'fs'

type Article = {
  id: string;
  title: string;
  content: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
};
const articles: Article[] = [];

// Create main app for all routes
const app = new Hono()

// Global middleware
app.use('*', requestId());
app.use('*', logger());
app.use('*', cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:4000'],
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

const model = google('gemini-2.5-pro')

// Static file serving for Vite build output
const staticPath = path.resolve(__dirname, '../../app/dist')

// Custom static file handler
const serveStaticFile = async (c: any, filePath: string) => {
  try {
    const fullPath = path.join(staticPath, filePath)
    
    // Security check - prevent directory traversal
    if (!fullPath.startsWith(staticPath)) {
      return c.notFound()
    }
    
    if (fs.existsSync(fullPath) && fs.statSync(fullPath).isFile()) {
      const content = fs.readFileSync(fullPath)
      const ext = path.extname(fullPath).toLowerCase()
      
      // Set appropriate content type
      const contentTypes: Record<string, string> = {
        '.html': 'text/html',
        '.js': 'application/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon'
      }
      
      const contentType = contentTypes[ext] || 'application/octet-stream'
      
      return new Response(content, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': ext === '.html' ? 'no-cache' : 'public, max-age=31536000'
        }
      })
    }
  } catch (error) {
    console.error('Error serving static file:', error)
  }
  
  return null
}

// Auth routes - Better Auth is configured with basePath: '/auth'
app.all('/auth/*', async (c) => {
  console.log('Auth handler received path:', c.req.path);
  try {
    const response = await auth.handler(c.req.raw);
    return response;
  } catch (error) {
    console.error('Error in auth handler:', error);
    c.status(500)
    return c.json({ error: 'Internal Server Error in auth handler', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// Also handle /api/auth/* for backward compatibility
app.all('/api/auth/*', async (c) => {
  console.log('API Auth handler received path:', c.req.path);
  try {
    const response = await auth.handler(c.req.raw);
    return response;
  } catch (error) {
    console.error('Error in API auth handler:', error);
    c.status(500)
    return c.json({ error: 'Internal Server Error in auth handler', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// API Routes
app.post('/api/chat', async (c) => {
  try {
    const body = await c.req.json()
    const { messages } = body as { messages: Array<Omit<UIMessage, 'id'>> }

    const result = await streamText({
      model,
      messages: convertToModelMessages(messages),
    })

    return result.toUIMessageStreamResponse()
  } catch (error: unknown) {
    console.error('Error in /api/chat:', error)
    return c.json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

app.post('/api/continue', async (c) => {
  try {
    const body = await c.req.json()
    const { text } = body as { text: string }

    const prompt = `
Help user keep writing; provide next prompt guides which could help user writing. Ask a direct question that prompts the user's next writing action based on their last text. Do not write new content; only ask or give a brief nudge. If no context is provided, ask what they are writing and their next goal. Always return one or two sentences;
the user's last text: ${text}
`

    const result = await streamText({
      model,
      prompt,
    })

    return result.toTextStreamResponse()
  } catch (error: unknown) {
    console.error('Error in /api/continue:', error)
    return c.json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

app.post('/api/suggest', async (c) => {
  try {
    const body = await c.req.json()
    const { text } = body as { text: string }

    const prompt = `
Help user keep writing; provide next prompt guides which could help user writing. Ask a direct question that prompts the user's next writing action based on their last text. Do not write new content; only ask or give a brief nudge. If no context is provided, ask what they are writing and their next goal. Always return one or two sentences;
the user's last text: ${text}
`

    const result = await streamText({
      model,
      prompt,
    })

    return result.toTextStreamResponse()
  } catch (error: unknown) {
    console.error('Error in /api/suggest:', error)
    return c.json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

const authMiddleware = createMiddleware(async (c, next) => {
  const cookieHeader = c.req.header('cookie') || '';
  const headers = new Headers();
  headers.set('cookie', cookieHeader);
  
  const session = await auth.api.getSession({
    headers,
  });

  if (!session) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  c.set('user', session.user);
  await next();
});

const articlesApp = new Hono<{ Variables: { user: any } }>();

articlesApp.use('*', authMiddleware);

articlesApp.get('/', (c) => {
  const user = c.get('user');
  const userArticles = articles.filter(a => a.userId === user.id);
  return c.json(userArticles);
});

articlesApp.post('/', async (c) => {
  const user = c.get('user');
  const { title, content } = await c.req.json();
  const newArticle: Article = {
    id: crypto.randomUUID(),
    title,
    content,
    userId: user.id,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  articles.push(newArticle);
  return c.json(newArticle, 201);
});

articlesApp.get('/:id', (c) => {
  const user = c.get('user');
  const { id } = c.req.param();
  const article = articles.find((a) => a.id === id);
  if (!article || article.userId !== user.id) {
    return c.json({ error: 'Article not found' }, 404);
  }
  return c.json(article);
});

articlesApp.put('/:id', async (c) => {
  const user = c.get('user');
  const { id } = c.req.param();
  const { title, content } = await c.req.json();
  const article = articles.find((a) => a.id === id);
  if (!article || article.userId !== user.id) {
    return c.json({ error: 'Article not found' }, 404);
  }
  article.title = title ?? article.title;
  article.content = content ?? article.content;
  article.updatedAt = new Date();
  return c.json(article);
});

articlesApp.delete('/:id', (c) => {
  const user = c.get('user');
  const { id } = c.req.param();
  const index = articles.findIndex((a) => a.id === id && a.userId === user.id);
  if (index === -1) {
    return c.json({ error: 'Article not found' }, 404);
  }
  articles.splice(index, 1);
  return c.json({ message: 'Article deleted' });
});

app.route('/api/articles', articlesApp);

// Hello World endpoint following Vercel docs
const welcomeStrings = [
  'Hello Hono!',
  'To learn more about Hono on Vercel, visit https://vercel.com/docs/frameworks/hono',
  'StartWrite API is running with Hono!'
]

// Health check endpoint
app.get('/api', (c) => {
  return c.text('StartWrite API is running with Hono!')
})

app.get('/hello', (c) => {
  return c.text(welcomeStrings.join('\n\n'))
})

// Static file serving (must be last to catch all unmatched routes)
app.get('*', async (c) => {
  const requestPath = c.req.path
  
  // Skip API routes (including auth)
  if (requestPath.startsWith('/api/')) {
    return c.notFound()
  }
  
  // Try to serve static file
  let filePath = requestPath === '/' ? '/index.html' : requestPath
  let response = await serveStaticFile(c, filePath)
  
  if (response) {
    return response
  }
  
  // SPA fallback - serve index.html for client-side routing
  response = await serveStaticFile(c, '/index.html')
  if (response) {
    return response
  }
  
  return c.notFound()
})

export default app

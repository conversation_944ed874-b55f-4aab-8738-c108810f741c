{"name": "@startwrite/api", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx src/dev.ts", "dev:watch": "tsx watch src/dev.ts", "dev:fullstack": "npm run build:app && npm run dev", "build:app": "cd ../app && npm run build", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "prisma:generate": "npx prisma generate --schema=../../prisma/schema.prisma"}, "dependencies": {"@ai-sdk/google": "^2.0.7", "@ai-sdk/react": "^2.0.15", "@hono/node-server": "^1.19.0", "@prisma/client": "^6.14.0", "@types/bcrypt": "^6.0.0", "@types/pg": "^8.15.5", "@vercel/node": "^3.0.0", "ai": "^5.0.15", "bcrypt": "^6.0.0", "better-auth": "^1.3.7", "hono": "^4.9.2", "pg": "^8.16.3", "pino": "^9.9.0", "prisma": "^6.14.0"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.7.1", "typescript": "^5.5.3"}}